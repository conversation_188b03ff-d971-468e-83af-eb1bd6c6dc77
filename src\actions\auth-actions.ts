"use server";

import { redirect } from "next/navigation";
import { cookies } from "next/headers";
import { authService } from "@/services/auth/auth-service";
import {
  LoginCredentials,
  LoginResponse,
  LogoutResponse,
} from "@/services/auth/types";
import { FormLoginData } from "@/schemas";

// Cookie configuration
const COOKIE_OPTIONS = {
  httpOnly: true,
  secure: process.env.NODE_ENV === "production",
  sameSite: "lax" as const,
  maxAge: 60 * 60 * 24 * 7, // 7 days
  path: "/",
};

/**
 * Server action for user login
 * Supports both old (username) and new (email) formats during Phase 1 transition
 */
export async function login(
  credentials: LoginCredentials | FormLoginData,
): Promise<LoginResponse> {
  try {
    // Convert email-based credentials to username format for mock auth compatibility
    // This is a temporary adapter for Phase 1 - will be removed in Phase 2
    let authCredentials: LoginCredentials;

    if ("email" in credentials) {
      // New email-based format - convert email to username for mock auth
      authCredentials = {
        username: credentials.email.split("@")[0], // Use email prefix as username
        password: credentials.password,
      };
    } else {
      // Old username format - use as is
      authCredentials = credentials;
    }

    // Call the auth service
    const result = await authService.login(authCredentials);

    if (result.success && result.data) {
      // Set secure HTTP-only cookies for authentication
      const cookieStore = await cookies();

      cookieStore.set(
        "auth_token",
        result.data.token.accessToken,
        COOKIE_OPTIONS,
      );
      cookieStore.set(
        "auth_user",
        JSON.stringify(result.data.user),
        COOKIE_OPTIONS,
      );

      // Set refresh token with longer expiry
      cookieStore.set("refresh_token", result.data.token.refreshToken, {
        ...COOKIE_OPTIONS,
        maxAge: 60 * 60 * 24 * 30, // 30 days
      });

      return result;
    }

    return result;
  } catch (error) {
    console.error("Login action error:", error);
    return {
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "An unexpected error occurred. Please try again.",
        details:
          error instanceof Error
            ? { message: error.message, stack: error.stack }
            : { error: String(error) },
      },
    };
  }
}

/**
 * Server action for user logout
 */
export async function logout(): Promise<LogoutResponse> {
  try {
    // Call the auth service
    const result = await authService.logout();

    // Clear authentication cookies
    const cookieStore = await cookies();
    cookieStore.delete("auth_token");
    cookieStore.delete("auth_user");
    cookieStore.delete("refresh_token");

    return result;
  } catch (error) {
    console.error("Logout action error:", error);
    return {
      success: false,
      message: "An error occurred during logout",
    };
  }
}

/**
 * Server action to get current authenticated user
 */
export async function getCurrentUser() {
  try {
    const cookieStore = await cookies();
    const authToken = cookieStore.get("auth_token")?.value;
    const userCookie = cookieStore.get("auth_user")?.value;

    if (!authToken || !userCookie) {
      return null;
    }

    // Validate the token
    const tokenValidation = await authService.validateToken(authToken);

    if (!tokenValidation.valid) {
      // Token is invalid, clear cookies
      cookieStore.delete("auth_token");
      cookieStore.delete("auth_user");
      cookieStore.delete("refresh_token");
      return null;
    }

    // Return the user from cookie (already validated by token)
    return JSON.parse(userCookie);
  } catch (error) {
    console.error("Get current user action error:", error);
    return null;
  }
}

/**
 * Server action to check if user is authenticated
 */
export async function getIsAuthenticated(): Promise<boolean> {
  try {
    const user = await getCurrentUser();
    return user !== null;
  } catch (error) {
    console.error("Is authenticated action error:", error);
    return false;
  }
}

/**
 * Server action to redirect to login if not authenticated
 */
export async function requireAuth(redirectTo?: string) {
  const isAuthenticated = await getIsAuthenticated();

  if (!isAuthenticated) {
    const loginUrl = redirectTo
      ? `/login?redirect=${encodeURIComponent(redirectTo)}`
      : "/login";
    redirect(loginUrl);
  }
}

/**
 * Server action to redirect authenticated users away from auth pages
 */
export async function redirectIfAuthenticated(
  redirectTo: string = "/dashboard",
) {
  const isAuthenticated = await getIsAuthenticated();

  if (isAuthenticated) {
    redirect(redirectTo);
  }
}

/**
 * Server action for form-based login with redirect
 * Updated to support email field (Phase 1 transition)
 */
export async function loginForm(formData: FormData) {
  // Support both email and username fields during transition
  const email = formData.get("email") as string;
  const username = formData.get("username") as string;
  const password = formData.get("password") as string;
  const redirectTo = formData.get("redirect") as string;

  const emailOrUsername = email || username;

  if (!emailOrUsername || !password) {
    return {
      success: false,
      error: {
        code: "MISSING_CREDENTIALS",
        message: "Email/username and password are required",
      },
    };
  }

  // Use email format if available, otherwise username
  const credentials = email ? { email, password } : { username, password };

  const result = await login(credentials);

  if (result.success) {
    // Redirect to dashboard or specified redirect URL
    const redirectUrl =
      redirectTo && redirectTo !== "null" ? redirectTo : "/dashboard";
    redirect(redirectUrl);
  }

  return result;
}

/**
 * Server action for logout with redirect
 */
export async function startLogout() {
  await logout();
  redirect("/login");
}
