/**
 * Hybrid Token Storage Strategy
 * Uses localStorage for client-side persistence (as per API docs)
 * and cookies for SSR compatibility
 */

/**
 * Token storage configuration
 */
const STORAGE_KEYS = {
  ACCESS_TOKEN: 'afreeserv_access_token',
  REFRESH_TOKEN: 'afreeserv_refresh_token', 
  USER: 'afreeserv_user',
  EXPIRES_AT: 'afreeserv_expires_at'
} as const;

/**
 * Client-side token storage using localStorage
 * Primary storage method as recommended in API documentation
 */
export class TokenStorage {
  /**
   * Store authentication tokens and user data
   */
  static setTokens(authResponse: AuthResponse): void {
    if (typeof window === 'undefined') return;

    try {
      // Store in localStorage (primary storage as per API docs)
      localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, authResponse.accessToken);
      localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, authResponse.refreshToken);
      localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(authResponse.admin));
      localStorage.setItem(STORAGE_KEYS.EXPIRES_AT, authResponse.expiresAt);
    } catch (error) {
      console.error('Failed to store tokens in localStorage:', error);
    }
  }

  /**
   * Get access token
   */
  static getAccessToken(): string | null {
    if (typeof window === 'undefined') return null;

    try {
      return localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
    } catch (error) {
      console.error('Failed to get access token:', error);
      return null;
    }
  }

  /**
   * Get refresh token
   */
  static getRefreshToken(): string | null {
    if (typeof window === 'undefined') return null;

    try {
      return localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
    } catch (error) {
      console.error('Failed to get refresh token:', error);
      return null;
    }
  }

  /**
   * Get stored user data
   */
  static getUser(): Admin | null {
    if (typeof window === 'undefined') return null;

    try {
      const userData = localStorage.getItem(STORAGE_KEYS.USER);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Failed to get user data:', error);
      return null;
    }
  }

  /**
   * Get token expiration time
   */
  static getExpiresAt(): string | null {
    if (typeof window === 'undefined') return null;

    try {
      return localStorage.getItem(STORAGE_KEYS.EXPIRES_AT);
    } catch (error) {
      console.error('Failed to get expiration time:', error);
      return null;
    }
  }

  /**
   * Check if tokens are expired
   */
  static isExpired(): boolean {
    const expiresAt = this.getExpiresAt();
    if (!expiresAt) return true;

    try {
      const expirationTime = new Date(expiresAt).getTime();
      const currentTime = Date.now();
      const bufferTime = 5 * 60 * 1000; // 5 minutes buffer
      
      return currentTime >= (expirationTime - bufferTime);
    } catch {
      return true;
    }
  }

  /**
   * Clear all stored tokens and user data
   */
  static clear(): void {
    if (typeof window === 'undefined') return;

    try {
      localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.USER);
      localStorage.removeItem(STORAGE_KEYS.EXPIRES_AT);
    } catch (error) {
      console.error('Failed to clear tokens:', error);
    }
  }

  /**
   * Check if user is authenticated (has valid tokens)
   */
  static isAuthenticated(): boolean {
    const accessToken = this.getAccessToken();
    const user = this.getUser();
    
    return !!(accessToken && user && !this.isExpired());
  }

  /**
   * Get all authentication data
   */
  static getAuthData(): {
    accessToken: string | null;
    refreshToken: string | null;
    user: Admin | null;
    expiresAt: string | null;
    isAuthenticated: boolean;
  } {
    return {
      accessToken: this.getAccessToken(),
      refreshToken: this.getRefreshToken(),
      user: this.getUser(),
      expiresAt: this.getExpiresAt(),
      isAuthenticated: this.isAuthenticated()
    };
  }

  /**
   * Update user data only (after profile updates)
   */
  static updateUser(user: Admin): void {
    if (typeof window === 'undefined') return;

    try {
      localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(user));
    } catch (error) {
      console.error('Failed to update user data:', error);
    }
  }
}

/**
 * Cookie configuration for server-side storage
 */
export const COOKIE_CONFIGS = {
  ACCESS_TOKEN: {
    name: 'auth_token',
    options: {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
      maxAge: 24 * 60 * 60, // 24 hours
      path: '/'
    }
  },
  REFRESH_TOKEN: {
    name: 'refresh_token',
    options: {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production', 
      sameSite: 'strict' as const,
      maxAge: 7 * 24 * 60 * 60, // 7 days
      path: '/'
    }
  },
  USER_DATA: {
    name: 'auth_user',
    options: {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
      maxAge: 24 * 60 * 60, // 24 hours
      path: '/'
    }
  },
  EXPIRES_AT: {
    name: 'auth_expires_at',
    options: {
      httpOnly: false, // Allow client-side access for expiry checking
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
      maxAge: 24 * 60 * 60, // 24 hours
      path: '/'
    }
  }
} as const;

/**
 * Cookie-based token storage for SSR compatibility
 * Used in server actions and middleware
 */
export class CookieTokenStorage {
  /**
   * Get access token from cookies (for middleware/server actions)
   */
  static getAccessToken(cookieStore: any): string | null {
    try {
      return cookieStore.get(COOKIE_CONFIGS.ACCESS_TOKEN.name)?.value || null;
    } catch {
      return null;
    }
  }

  /**
   * Get user data from cookies
   */
  static getUser(cookieStore: any): Admin | null {
    try {
      const userData = cookieStore.get(COOKIE_CONFIGS.USER_DATA.name)?.value;
      return userData ? JSON.parse(userData) : null;
    } catch {
      return null;
    }
  }

  /**
   * Get expiration time from cookies
   */
  static getExpiresAt(cookieStore: any): string | null {
    try {
      return cookieStore.get(COOKIE_CONFIGS.EXPIRES_AT.name)?.value || null;
    } catch {
      return null;
    }
  }

  /**
   * Check if tokens are expired (server-side)
   */
  static isExpired(cookieStore: any): boolean {
    const expiresAt = this.getExpiresAt(cookieStore);
    if (!expiresAt) return true;

    try {
      const expirationTime = new Date(expiresAt).getTime();
      const currentTime = Date.now();
      const bufferTime = 5 * 60 * 1000; // 5 minutes buffer
      
      return currentTime >= (expirationTime - bufferTime);
    } catch {
      return true;
    }
  }

  /**
   * Clear cookies (for server actions)
   */
  static clearTokens(cookieStore: any): void {
    try {
      cookieStore.delete(COOKIE_CONFIGS.ACCESS_TOKEN.name);
      cookieStore.delete(COOKIE_CONFIGS.USER_DATA.name);
      cookieStore.delete(COOKIE_CONFIGS.REFRESH_TOKEN.name);
      cookieStore.delete(COOKIE_CONFIGS.EXPIRES_AT.name);
    } catch (error) {
      console.error('Failed to clear cookies:', error);
    }
  }
}
