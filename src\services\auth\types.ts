// Authentication types and interfaces

export interface User {
  id: string;
  username: string;
  firstName: string;
  lastName: string;
}

export interface AuthToken {
  accessToken: string;
  refreshToken: string;
  expiresAt: Date;
  tokenType: "Bearer";
}

export interface AuthResponse {
  user: User;
  token: AuthToken;
  message: string;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: AuthToken | null;
  loading: boolean;
  error: string | null;
}

export interface TokenPayload {
  userId: string;
  username: string;
  iat: number;
  exp: number;
}

// Mock data interfaces
export interface MockUser extends User {
  password: string; // Only for mock data
}

export interface AuthError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
}

// API Response types
export interface LoginResponse {
  success: boolean;
  data?: AuthResponse;
  error?: AuthError;
}

export interface LogoutResponse {
  success: boolean;
  message: string;
}

export interface ValidateTokenResponse {
  valid: boolean;
  user?: User;
  error?: AuthError;
}
