import {
  AsyncServiceDetails,
  ServiceDetailsSkeleton,
} from "@/components/features/service-details";
import { SearchParams } from "nuqs";
import { Suspense } from "react";
import { ProtectedPage } from "@/components/auth/protected-page";

interface ServicesDetailsPageProps {
  params: Promise<{ id: string }>;
  searchParams: Promise<SearchParams>;
}

export default function ServicesDetailsPage({
  params,
  searchParams,
}: ServicesDetailsPageProps) {
  return (
    <ProtectedPage redirectTo="/login">
      <div className="flex flex-col gap-2.5 py-2.5 px-5">
        <Suspense fallback={<ServiceDetailsSkeleton />}>
          <AsyncServiceDetails params={params} searchParams={searchParams} />
        </Suspense>
      </div>
    </ProtectedPage>
  );
}
