import { MockUser } from "./types";

// Mock users database
export const MOCK_USERS: MockUser[] = [
  {
    id: "1",
    username: "admin",
    password: "admin123",
    firstName: "Admin",
    lastName: "User",
  },
];

// Mock token expiration times
export const TOKEN_EXPIRY = {
  ACCESS_TOKEN: 15 * 60 * 1000, // 15 minutes
  REFRESH_TOKEN: 7 * 24 * 60 * 60 * 1000, // 7 days
};

// Helper function to find user by username
export const findUserByUsername = (username: string): MockUser | undefined => {
  return MOCK_USERS.find((user) => user.username === username);
};

// Helper function to find user by ID
export const findUserById = (id: string): MockUser | undefined => {
  return MOCK_USERS.find((user) => user.id === id);
};

// Helper function to validate password (in real app, this would use bcrypt)
export const validatePassword = (
  plainPassword: string,
  userPassword: string,
): boolean => {
  return plainPassword === userPassword; // Simple comparison for mock
};

// Helper function to convert MockUser to User (removing password)
export const sanitizeUser = (mockUser: MockUser) => {
  const { password: _password, ...user } = mockUser;
  return user;
};

// Mock token generation (in real app, this would use a proper JWT library)
export const generateMockToken = (user: MockUser): string => {
  const payload = {
    userId: user.id,
    username: user.username,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor((Date.now() + TOKEN_EXPIRY.ACCESS_TOKEN) / 1000),
  };

  // In a real app, you would use jsonwebtoken library
  // return jwt.sign(payload, MOCK_JWT_SECRET);

  // For mock purposes, we'll create a simple base64 encoded token
  return btoa(JSON.stringify(payload));
};

// Mock token validation
export const validateMockToken = (
  token: string,
): { valid: boolean; payload?: string } => {
  try {
    // In a real app, you would use jwt.verify(token, MOCK_JWT_SECRET)
    const payload = JSON.parse(atob(token));

    // Check if token is expired
    const now = Math.floor(Date.now() / 1000);
    if (payload.exp < now) {
      return { valid: false };
    }

    return { valid: true, payload };
  } catch (_error) {
    return { valid: false };
  }
};
