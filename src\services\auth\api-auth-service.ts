/**
 * Real API Authentication Service
 * Replaces the mock authentication with actual API calls
 * Based on the API specification from api_doc.json
 */

/**
 * API Authentication Service Class
 * Handles all authentication-related API calls
 */
export class ApiAuthService {
  private readonly apiUrl: string;
  private readonly endpoints = {
    login: '/admin/auth/login',
    profile: '/admin/auth/profile',
    register: '/admin/auth/register',
    testPermissions: '/admin/auth/test-permissions',
    testRoles: '/admin/auth/test-roles'
  };

  constructor() {
    this.apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';
  }

  /**
   * Authenticate admin user with email and password
   * POST /admin/auth/login
   */
  async login(credentials: AdminLoginRequest): Promise<ApiResponse<AuthResponse>> {
    try {
      const response = await fetch(`${this.apiUrl}${this.endpoints.login}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          'X-Afreeserv-Client': 'admin-panel'
        },
        body: JSON.stringify(credentials)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        return {
          success: false,
          error: errorData.message || 'Login failed',
          statusCode: response.status
        };
      }

      const data: AuthResponse = await response.json();
      
      // Validate response structure
      if (!this.isValidAuthResponse(data)) {
        return {
          success: false,
          error: 'Invalid response format from server'
        };
      }

      return {
        success: true,
        data
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error occurred'
      };
    }
  }

  /**
   * Get current admin profile (used for token validation)
   * GET /admin/auth/profile
   * Since refresh endpoint is not available, we use this for validation
   */
  async getProfile(accessToken: string): Promise<ApiResponse<Admin>> {
    try {
      const response = await fetch(`${this.apiUrl}${this.endpoints.profile}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          'X-Afreeserv-Client': 'admin-panel'
        }
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        return {
          success: false,
          error: errorData.message || 'Failed to get profile',
          statusCode: response.status
        };
      }

      const data: Admin = await response.json();
      
      return {
        success: true,
        data
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error occurred'
      };
    }
  }

  /**
   * Validate token by attempting to get profile
   * This replaces the missing refresh endpoint functionality
   */
  async validateToken(accessToken: string): Promise<TokenValidationResponse> {
    if (!accessToken) {
      return { valid: false, error: 'No token provided' };
    }

    const result = await this.getProfile(accessToken);
    
    if (result.success && result.data) {
      return { valid: true, admin: result.data };
    }

    return { 
      valid: false, 
      error: result.error || 'Token validation failed' 
    };
  }

  /**
   * Register new admin (Super Admin only)
   * POST /admin/auth/register
   */
  async register(
    adminData: AdminRegistrationFormData,
    accessToken: string
  ): Promise<ApiResponse<Admin>> {
    try {
      const response = await fetch(`${this.apiUrl}${this.endpoints.register}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          'X-Afreeserv-Client': 'admin-panel'
        },
        body: JSON.stringify(adminData)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        return {
          success: false,
          error: errorData.message || 'Registration failed',
          statusCode: response.status
        };
      }

      const data: Admin = await response.json();
      
      return {
        success: true,
        data
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error occurred'
      };
    }
  }

  /**
   * Test permissions endpoint
   * GET /admin/auth/test-permissions
   */
  async testPermissions(accessToken: string): Promise<ApiResponse<unknown>> {
    return this.makeAuthenticatedRequest(this.endpoints.testPermissions, accessToken);
  }

  /**
   * Test roles endpoint
   * GET /admin/auth/test-roles
   */
  async testRoles(accessToken: string): Promise<ApiResponse<unknown>> {
    return this.makeAuthenticatedRequest(this.endpoints.testRoles, accessToken);
  }

  /**
   * Generic authenticated request helper
   */
  private async makeAuthenticatedRequest(
    endpoint: string, 
    accessToken: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<unknown>> {
    try {
      const response = await fetch(`${this.apiUrl}${endpoint}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          'X-Afreeserv-Client': 'admin-panel',
          ...options.headers
        },
        ...options
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        return {
          success: false,
          error: errorData.message || 'Request failed',
          statusCode: response.status
        };
      }

      const data = await response.json();
      
      return {
        success: true,
        data
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error occurred'
      };
    }
  }

  /**
   * Validate AuthResponse structure
   */
  private isValidAuthResponse(data: unknown): data is AuthResponse {
    if (!data || typeof data !== 'object') return false;
    
    const response = data as Record<string, unknown>;
    
    return (
      typeof response.accessToken === 'string' &&
      typeof response.refreshToken === 'string' &&
      typeof response.expiresAt === 'string' &&
      response.admin &&
      typeof response.admin === 'object'
    );
  }

  /**
   * Check if token is expired based on expiresAt timestamp
   */
  isTokenExpired(expiresAt: string): boolean {
    try {
      const expirationTime = new Date(expiresAt).getTime();
      const currentTime = Date.now();
      const bufferTime = 5 * 60 * 1000; // 5 minutes buffer
      
      return currentTime >= (expirationTime - bufferTime);
    } catch {
      return true; // Assume expired if we can't parse the date
    }
  }

  /**
   * Logout (clear tokens - no server endpoint needed)
   */
  async logout(): Promise<ApiResponse<void>> {
    // Since there's no logout endpoint in the API spec,
    // we just return success - token clearing is handled by TokenStorage
    return {
      success: true
    };
  }
}

// Singleton instance
export const apiAuthService = new ApiAuthService();
