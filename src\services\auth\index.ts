// Authentication service exports
export { AuthService, authService } from "./auth-service";
export * from "./types";
export * from "./mock-data";

// Re-export commonly used types for convenience
export type {
  User,
  AuthToken,
  AuthResponse,
  LoginCredentials,
  AuthState,
  LoginResponse,
  LogoutResponse,
  ValidateTokenResponse,
} from "./types";

// No enums to export in simplified auth system

// Re-export mock utilities
export {
  MOCK_USERS,
  findUserByUsername,
  findUserById,
  validatePassword,
  sanitizeUser,
  generateMockToken,
  validateMockToken,
} from "./mock-data";
