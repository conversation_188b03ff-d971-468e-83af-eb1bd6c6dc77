import { ReactNode } from "react";
import { redirect } from "next/navigation";
import { getCurrentUser } from "@/actions/auth-actions";
interface ProtectedPageProps {
  children: ReactNode;
  redirectTo?: string;
}

/**
 * Server component wrapper for protected pages
 * Provides basic authentication check - redirects unauthenticated users to login
 * All authenticated users receive full access (admin-only system)
 */
export async function ProtectedPage({
  children,
  redirectTo = "/login",
}: ProtectedPageProps) {
  // Get current user
  const user = await getCurrentUser();

  // Redirect if not authenticated
  if (!user) {
    redirect(redirectTo);
  }

  // User is authenticated, show content
  return <>{children}</>;
}
