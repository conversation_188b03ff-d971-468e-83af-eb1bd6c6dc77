import { BaseApiService as BaseService } from "../base-service";
import {
  User,
  AuthToken,
  LoginCredentials,
  LoginResponse,
  LogoutResponse,
  ValidateTokenResponse,
  TokenPayload,
} from "./types";
import {
  findUserByUsername,
  findUserById,
  validatePassword,
  sanitizeUser,
  generateMockToken,
  validateMockToken,
  TOKEN_EXPIRY,
  validateMockToken,
} from "./mock-data";
import { apiAuthService } from "./api-auth-service";
import { TokenStorage } from "./token-storage";
import { env } from "@/config/env";

export class AuthService extends BaseService {
  private static instance: AuthService;
  private currentUser: User | null = null;
  private currentToken: AuthToken | null = null;
  private useApiAuth: boolean = false; // Toggle between mock and API auth

  constructor() {
    super();
    // Use API auth in production or when explicitly enabled
    this.useApiAuth = env.isProduction || process.env.USE_API_AUTH === "true";
  }

  // Singleton pattern to ensure single instance
  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  /**
   * Authenticate user with username and password
   * Supports both mock and API authentication
   */
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      // Use API authentication if enabled
      if (this.useApiAuth) {
        return await this.loginWithApi(credentials);
      }

      // Fall back to mock authentication
      return await this.loginWithMock(credentials);
    } catch (error) {
      return {
        success: false,
        error: {
          code: "LOGIN_ERROR",
          message: "An error occurred during login. Please try again.",
          details:
            error instanceof Error
              ? { message: error.message, stack: error.stack }
              : { error: String(error) },
        },
      };
    }
  }

  /**
   * API-based authentication
   */
  private async loginWithApi(
    credentials: LoginCredentials,
  ): Promise<LoginResponse> {
    // Convert username-based credentials to email format for API
    const apiCredentials: AdminLoginRequest = {
      email:
        "username" in credentials
          ? `${credentials.username}@afreeserv.com`
          : credentials.email,
      password: credentials.password,
    };

    const result = await apiAuthService.login(apiCredentials);

    if (result.success && result.data) {
      // Store tokens using TokenStorage
      TokenStorage.setTokens(result.data);

      // Convert API response to legacy format for compatibility
      const legacyResponse: LoginResponse = {
        success: true,
        data: {
          user: {
            id: result.data.admin.id.toString(),
            username: result.data.admin.email.split("@")[0],
            firstName: result.data.admin.firstName,
            lastName: result.data.admin.lastName,
          },
          token: {
            accessToken: result.data.accessToken,
            refreshToken: result.data.refreshToken,
            expiresAt: new Date(result.data.expiresAt),
            tokenType: "Bearer",
          },
          message: "Login successful",
        },
      };

      return legacyResponse;
    }

    return {
      success: false,
      error: {
        code: "API_LOGIN_FAILED",
        message: result.error || "API login failed",
      },
    };
  }

  /**
   * Mock authentication (existing implementation)
   */
  private async loginWithMock(
    credentials: LoginCredentials,
  ): Promise<LoginResponse> {
    try {
      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const { username, password } = credentials;

      // Find user by username
      const mockUser = findUserByUsername(username);
      if (!mockUser) {
        return {
          success: false,
          error: {
            code: "USER_NOT_FOUND",
            message: "Invalid username or password",
          },
        };
      }

      // Validate password
      if (!validatePassword(password, mockUser.password)) {
        return {
          success: false,
          error: {
            code: "INVALID_CREDENTIALS",
            message: "Invalid username or password",
          },
        };
      }

      // In simplified auth, all users are considered active

      // Generate tokens
      const accessToken = generateMockToken(mockUser);
      const refreshToken = generateMockToken(mockUser); // In real app, refresh token would be different

      const authToken: AuthToken = {
        accessToken,
        refreshToken,
        expiresAt: new Date(Date.now() + TOKEN_EXPIRY.ACCESS_TOKEN),
        tokenType: "Bearer",
      };

      // Convert mock user to clean user object
      const user = sanitizeUser(mockUser);

      // Store current session
      this.currentUser = user;
      this.currentToken = authToken;

      // Store in localStorage for persistence (in real app, you might use httpOnly cookies)
      if (typeof window !== "undefined") {
        localStorage.setItem("auth_token", accessToken);
        localStorage.setItem("auth_user", JSON.stringify(user));
      }

      return {
        success: true,
        data: {
          user,
          token: authToken,
          message: "Login successful",
        },
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: "LOGIN_ERROR",
          message: "An error occurred during login. Please try again.",
          details:
            error instanceof Error
              ? { message: error.message, stack: error.stack }
              : { error: String(error) },
        },
      };
    }
  }

  /**
   * Logout current user
   */
  async logout(): Promise<LogoutResponse> {
    try {
      // Clear current session
      this.currentUser = null;
      this.currentToken = null;

      // Clear localStorage
      if (typeof window !== "undefined") {
        localStorage.removeItem("auth_token");
        localStorage.removeItem("auth_user");
      }

      return {
        success: true,
        message: "Logout successful",
      };
    } catch (_error) {
      return {
        success: false,
        message: "An error occurred during logout",
      };
    }
  }

  /**
   * Get current authenticated user
   */
  async getCurrentUser(): Promise<User | null> {
    // If we have a current user in memory, return it
    if (this.currentUser) {
      return this.currentUser;
    }

    // Try to restore from localStorage
    if (typeof window !== "undefined") {
      const storedUser = localStorage.getItem("auth_user");
      const storedToken = localStorage.getItem("auth_token");

      if (storedUser && storedToken) {
        // Validate the stored token
        const tokenValidation = await this.validateToken(storedToken);
        if (tokenValidation.valid && tokenValidation.user) {
          this.currentUser = tokenValidation.user;
          return this.currentUser;
        }
      }
    }

    return null;
  }

  /**
   * Validate authentication token
   */
  async validateToken(token: string): Promise<ValidateTokenResponse> {
    try {
      const validation = validateMockToken(token);

      if (!validation.valid) {
        return {
          valid: false,
          error: {
            code: "INVALID_TOKEN",
            message: "Token is invalid or expired",
          },
        };
      }

      const payload = validation.payload as unknown as TokenPayload;
      const mockUser = findUserById(payload.userId);

      if (!mockUser) {
        return {
          valid: false,
          error: {
            code: "USER_NOT_FOUND",
            message: "User not found",
          },
        };
      }

      const user = sanitizeUser(mockUser);
      return {
        valid: true,
        user,
      };
    } catch (error) {
      return {
        valid: false,
        error: {
          code: "TOKEN_VALIDATION_ERROR",
          message: "Error validating token",
          details:
            error instanceof Error
              ? { message: error.message, stack: error.stack }
              : { error: String(error) },
        },
      };
    }
  }

  /**
   * Check if user is authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    const user = await this.getCurrentUser();
    return user !== null;
  }

  /**
   * Get current auth token
   */
  getCurrentToken(): AuthToken | null {
    return this.currentToken;
  }
}

// Export singleton instance
export const authService = AuthService.getInstance();
