# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.1.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html)

---

## [0.6.0] - 2025-07-24

### Added

- **API-Compliant Authentication Types**: Complete type definitions matching API DOC specification

  - `Admin` type with all 10+ required fields (id, email, firstName, lastName, fullName, avatar, status, lastLoginAt, roles, permissions)
  - `AdminLoginRequest` type for email-based authentication requests
  - `AuthResponse` type matching API response structure exactly
  - `AdminStatus` enumeration with ACTIVE/INACTIVE/SUSPENDED values
  - Complete role and permission system types (super_admin, admin, moderator, support)
  - Global type declarations following project pattern from `src/types/services/index.d.ts`

- **Email-Based Login Schema**: Updated Zod validation schema for API compliance

  - Changed login field from `username` to `email` with proper email validation
  - Enhanced password validation with configurable length requirements
  - Standardized error message patterns across all form validations
  - Added validation helper functions for real-time form validation

- **Admin Registration Schema**: New schema for creating admin accounts (super admin only)

  - Complete registration form validation (email, firstName, lastName, password)
  - Enhanced password complexity requirements (8+ characters with uppercase, lowercase, number, special character)
  - Password complexity checker with detailed feedback for users

### Changed

- **Authentication Form Field**: Updated login form from username to email field

  - Changed input type from text to email for better UX and validation
  - Updated form icon from IconUser to IconMail
  - Modified form validation to use new email-based schema

- **Type Definition Architecture**: Migrated from direct exports to global declarations

  - All authentication types now available globally without imports
  - Consistent with existing project pattern in `src/types/services/index.d.ts`
  - Zod schema inference for automatic type generation from validation schemas

- **Server Actions Compatibility**: Enhanced auth actions to support both username and email during transition
  - Backward compatibility adapter for Phase 1 implementation
  - Support for both old (username) and new (email) authentication formats
  - Temporary email-to-username conversion for mock authentication compatibility

### Breaking Changes

- **Login Form Field Change**: Login form now uses `email` field instead of `username`
  - Forms expecting `username` field will need to be updated to use `email`
  - API calls now expect email-based credentials matching `AdminLoginRequest` interface
  - Translation keys updated to reflect email-based authentication

### Technical Details

- **API Specification Compliance**: All types match API DOCS specification exactly

  - Admin ID changed from string to number (as per API specification)
  - Complete field mapping for all API response structures
  - Role hierarchy system with privilege levels (1=highest, 4=lowest)
  - Granular permission system (users:read, services:create, etc.)

- **Type Safety Improvements**: Enhanced TypeScript coverage and validation

  - Comprehensive type guards for runtime validation
  - Eliminated `any` types in favor of `unknown` with proper type checking
  - Reduced function complexity while maintaining type safety

- **Schema Validation**: Robust form validation with detailed error handling
  - Email format validation with length constraints
  - Password complexity requirements with real-time feedback
  - Consistent error message patterns for internationalization support

### Files Created

- `src/types/auth/index.d.ts` - Global authentication type declarations
- `src/schemas/form-admin-registration-schema.ts` - Admin registration validation schema

### Files Modified

- `src/schemas/form-login-schema.ts` - Updated to email-based authentication with clean code principles
- `src/schemas/index.ts` - Added new schema exports
- `src/components/forms/auth-form.tsx` - Updated to use email field and new type definitions
- `src/actions/auth-actions.ts` - Enhanced compatibility for username/email transition

### Migration Notes

- This is Phase 1 of the authentication system migration from mock to real API
- Backward compatibility maintained during transition period
- Legacy types marked as deprecated but still functional
- Gradual migration path prepared for subsequent implementation phases

---

## [0.5.0] - 2025-07-19

### Added

- **Complete Authentication System**: Implemented comprehensive auth service with JWT token handling
- **Login Form**: Created login form with React Hook Form + Zod validation and toast notifications
- **Route Protection**: Added middleware for route protection with next-intl integration
- **Auth Guards**: Implemented auth guards and protected page components for access control
- **Route Groups**: Created auth and dashboard route groups with proper layouts
- **Account Management**: Updated account dropdown to show logout option only
- **Internationalization**: Added French translations for auth-related UI elements
- **Admin-Only Access**: Simplified authentication system with single admin user (admin/admin123)

### Changed

- **App Structure**: Restructured app routes into (auth) and (dashboard) groups
- **Authentication Flow**: All authenticated users receive full access without role/permission restrictions
- **Middleware**: Enhanced middleware to handle both authentication and internationalization

### Fixed

- **ESLint Errors**: Resolved TypeScript and ESLint errors in service components
- **Type Safety**: Fixed type assertions and removed deprecated components
- **Code Cleanup**: Removed obsolete service details implementation

### Technical Details

- Uses mock authentication with single admin user for development
- JWT-based session management with secure cookie storage
- Next.js 14 App Router with proper route group organization
- Integration with next-intl for multilingual support

---

## [0.4.0] - 2025-07-16

### Added

- Comprehensive server actions implementation for all CRUD operations
- React Query integration with server actions for client-side caching
- Async/sync component separation pattern for better performance
- Skeleton loading states with Suspense boundaries
- Dedicated error components for improved user feedback
- Comprehensive logging system for debugging data flow issues
- Type-safe server action implementations with proper error handling

### Changed

- **Service Architecture Modernization:**

  - Renamed `ServicesServiceClass` to `ServicesServices` following naming conventions
  - Updated service methods from `getServices`/`getServiceById` to `list`/`get`
  - Migrated from client-side `BaseApiService` to server-side `BaseService`
  - Implemented proper request tagging for caching optimization

- **Data Fetching Pattern Overhaul:**

  - Replaced custom hooks with React Query + server actions pattern
  - Removed deprecated hook-based data fetching approach
  - Added proper query key management for React Query caching
  - Implemented stale time and garbage collection configuration

- **Component Architecture Improvements:**

  - Introduced `AsyncServicesTable` for server-side data fetching
  - Created `ServicesTableWithQuery` for client-side React Query integration
  - Added proper component separation between async and sync components
  - Updated component props to use schema-based types

- **Type System Enhancements:**
  - Updated type definitions to use proper Zod schema-based types
  - Improved type safety across service layer and components
  - Added proper TypeScript interfaces for all component props

### Removed

- Deprecated custom hooks for direct service calls
- Client-side `BaseApiService` usage in favor of server-side patterns
- Legacy component patterns that mixed data fetching with presentation

## [0.3.0] - 2025-07-13

### Added

- Complete service details page implementation with comprehensive service information display
- Service images component with timeout handling and retry functionality
- Provider information section with contact details and ratings
- Pricing details with currency support and responsive design
- Service categories and locations display
- Service status indicators and date information
- Popup manager system for user interactions (reject, delete, approve actions)
- Enhanced image loading with fallback handling for Google Cloud Storage timeouts
- Root layout for Next.js App Router compliance
- Global type definitions for popup manager system
- CSS variables integration for consistent theming

### Changed

- Migrated popup manager types to global declarations in index.d.ts
- Updated image components to use CSS variables instead of hardcoded colors
- Improved service image handling with retry logic and timeout management
- Enhanced error handling for external image loading
- Simplified popup manager to support only textarea inputs by default
- Updated Next.js configuration for better image optimization
- Removed hardcoded sizes in favor of responsive Tailwind classes

## [0.2.0] - 2025-07-08

### Added

- Native React table implementation to replace TanStack Table
- Custom table hooks with sorting, filtering, and search functionality
- Unified search interface with column selector dropdown
- Table pagination with URL parameter synchronization
- Modular column definitions in separate files
- French accent-aware sorting using localeCompare
- Clipboard copy functionality for table cells
- Translation keys for table search and filters

### Changed

- Migrated from TanStack Table to native React table implementation
- Refactored table columns into individual files for better maintainability
- Updated search filter to use unified input with integrated dropdown
- Improved table performance by removing external dependencies
- Enhanced internationalization with proper translation keys

### Removed

- TanStack Table dependency and related code
- Test table components and mock data
- Unused table utility functions
- Legacy table column header component

### Fixed

- French character sorting in table columns
- Translation key errors in search components
- TypeScript compilation errors
- Build process optimization

Implement comprehensive UI components for services management (Step 2 && Step 3)

### Added

- **Advanced Services Data Table:** Implemented a feature-rich data table for the services list using TanStack Table, including server-side pagination, multi-column sorting, and filtering capabilities.
- **Comprehensive Filtering:** Introduced filters for service name (text search), status, and date range, with all states synchronized to the URL for shareable views.
- **Custom Pagination:** Developed a custom, numbered pagination component with ellipsis for navigating large datasets efficiently.
- **Modular Table Components:** The new table is built with a modular structure, separating logic for columns, filters, pagination, and data rendering for better maintainability.
- **New Application Layout:** Established a new, scalable `AppLayout` with a persistent, data-driven sidebar and a modern application header.
- **Dynamic Sidebar Navigation:** Created a fully collapsible, data-driven sidebar using `lucide-react` icons, with active state management based on the current route.
- **Account Dropdown & UI Components:** Added a new user account dropdown in the header and a `TruncatedText` utility component for handling long text with tooltips.
- **i18n Expansion:** Massively expanded translation keys for `en` and `fr` locales, covering all new UI elements, including the services table, filters, navigation, and auth flows.
- **Developer Experience (DX):** Added a `.vscode/settings.json` file to enforce code style, organize imports, and auto-format on save.

### Changed

- **UI/UX Overhaul:** Completely revamped the global stylesheet (`globals.css`) with a new design system based on Figma. Introduced new color variables for primary, secondary, and stateful UI elements (success, warning, destructive).
- **ESLint Configuration:** Refactored the ESLint setup from a legacy `FlatCompat` configuration to a modern `tseslint` flat config, improving type-awareness and simplifying rules.
- **Global State Management:** Enhanced the `useGlobalQueryParams` hook to manage the entire state of the services table (pagination, filters, search) directly in the URL via `nuqs`.

### Removed

- **Old Services Implementation:** Deleted the previous, basic `ServicesTable` and its related test components.
- **Redundant ESLint Rules:** Cleaned up the ESLint configuration, removing overly restrictive or outdated rules in favor of a simpler, more modern ruleset.

### Next Steps

- **Step 4:** Implement service detail view and "View More" functionality.
- **Step 5:** Implement mutation actions (Create, Update, Delete) for services.

---

## [0.1.1] - 2025-07-8

### Added

- **Services Architecture Implementation (Step 1):**
  Complete implementation of the services layer following Clean Architecture patterns with proper separation of concerns.

- **Modular Schema Structure:**

  - Decomposed monolithic services schema into focused, maintainable files:
    - `provider-schema.ts` - Provider-related schemas and validations
    - `service-schema.ts` - Service entities, categories, and pricing schemas
    - `pagination-schema.ts` - Pagination parameters and response schemas
    - `support-schema.ts` - Support ticket and request schemas
    - `services-list-response-schema.ts` - API response schemas
  - Centralized exports using `export *` pattern in `index.ts`

- **Global TypeScript Type System:**

  - Implemented proper global type declarations using `declare global` pattern
  - All service-related types available globally without imports
  - Type-safe integration with Zod schemas using `z.infer<typeof Schema>`
  - Resolved TypeScript module resolution issues for seamless development

- **Client-Side Compatible API Service:**

  - Created `BaseApiService` class extending existing patterns
  - Client-side token management using localStorage
  - Proper server/client component separation for Next.js App Router
  - Comprehensive error handling with custom `ApiError` class
  - URL construction with query parameter support

- **ServicesService Implementation:**

  - `getServices()` method with proper parameter handling
  - URL construction following API specification: `/services/all/{limit}/{next}`
  - Optional `extl_id` query parameter support
  - Schema validation using `ServicesListResponseSchema`
  - TODO placeholders for future methods (getServiceById, createService, etc.)

- **React Query Integration:**

  - `useServices` hook with TanStack Query for optimized data fetching
  - Automatic URL parameter state management using nuqs integration
  - Intelligent caching with proper query keys and stale time configuration
  - Background refetching and error retry mechanisms
  - Placeholder hooks for `useService` and `useUserServices`

- **Mutation Actions with TanStack Query:**

  - Comprehensive mutation hooks: `useCreateService`, `useUpdateService`, `useDeleteService`, `useValidateService`
  - Automatic cache invalidation on successful mutations
  - User feedback with toast notifications
  - Proper error handling with user-friendly messages
  - Optimistic updates and rollback capabilities

- **Modern UI Components:**
  - `ServicesTable` component with loading states and error handling
  - Integration with shadcn/ui components (Table, Badge, Skeleton, Alert)
  - Responsive design with proper data display
  - Internationalization support with next-intl
  - Test components for development and debugging

### Fixed

- **Server/Client Component Compatibility:**

  - Resolved `next/headers` import error in client-side components
  - Separated server-side and client-side concerns properly
  - Fixed font loading issues with Google Fonts and Turbopack
  - Eliminated server-only API usage in client components

- **URL Construction Issues:**

  - Fixed duplicate query parameters in API requests
  - Corrected API endpoint construction: `/services/all/{limit}/{next}`
  - Proper handling of optional `extl_id` parameter
  - Removed redundant query parameters from URL path

- **TypeScript Global Types:**
  - Fixed global type declaration pattern using `declare global`
  - Resolved module resolution issues for service types
  - Proper integration with existing global type system

### Changed

- **Architecture Alignment:**

  - Refactored existing implementation to follow established project patterns
  - Aligned with Clean Architecture principles and file organization
  - Updated service layer to extend `BaseApiService` properly
  - Improved error handling and response processing

- **Development Experience:**
  - Enhanced debugging capabilities with test components
  - Better error messages and logging for development
  - Improved TypeScript intellisense and type safety
  - Streamlined development workflow with proper tooling

### Technical Details

- **File Structure:** Follows established patterns with max 100-130 lines per file
- **Type Safety:** Full TypeScript coverage with global type declarations
- **Caching Strategy:** Optimized with TanStack Query for performance
- **URL Management:** nuqs integration for automatic URL state synchronization
- **Error Handling:** Comprehensive error boundaries and user feedback
- **Testing:** Test components and debugging tools for development

### Next Steps

- **Step 2:** Implement comprehensive UI components for services management
- **Step 3:** Add filtering, sorting, and search functionality

---

## [0.1.0] - 2025-07-8

### Added

- **Modern Next.js 15+ project foundation:**
  Established a robust project base using Next.js 15+ with the App Router, TypeScript, and a modular directory structure to support scalable development.

- **Internationalization (i18n) support:**
  Integrated next-intl for seamless multi-language support, including configuration, routing, and locale management.

- **UI component system:**
  Set up a comprehensive UI library using shadcn/ui and Radix UI primitives, providing reusable, accessible components for rapid interface development.

- **Form management and validation:**
  Implemented react-hook-form for efficient form state management and zod for schema-based validation, ensuring reliable user input handling.

- **State management architecture:**
  Introduced a type-safe global state management solution within the `src/store/` directory, enabling predictable and maintainable state handling.

- **Testing infrastructure:**
  Configured Jest for unit testing and Cypress for end-to-end testing, with supporting documentation in `TESTING_TOOLS.md` to guide contributors.

- **Development tooling and automation:**

  - ESLint and Prettier for code quality and formatting consistency.
  - Husky and lint-staged for automated pre-commit checks.
  - Tailwind CSS and PostCSS for utility-first styling and CSS processing.
  - pnpm as the preferred package manager for efficient dependency management.

- **Comprehensive documentation:**

  - `README.md` with setup, development, and contribution guidelines.
  - `project.md` outlining project structure, conventions, and architectural decisions.
  - Example files and directories to illustrate best practices for data fetching, state management, and service layer design.

- **Utility and support modules:**
  - Custom hooks in `src/hooks/` for common React patterns.
  - Validation schemas in `src/schemas/`.
  - Service layer abstraction in `src/services/`.
  - Type definitions in `src/types/`.
  - Shared utilities in `src/lib/`.

### Changed

- **Project structure enhancements:**

  - Adopted a clean, modular architecture with clear separation of concerns.
  - Organized source code into domain-specific directories for maintainability and scalability.
  - Updated TypeScript configuration for strict type safety and improved developer experience.

- **Tooling and configuration improvements:**
  - Migrated from npm to pnpm for faster, more reliable dependency management.
  - Upgraded Node.js version requirement to 23.5.0 to leverage the latest features and performance improvements.

### Removed

- **Legacy and redundant files:**
  - Eliminated default Next.js app structure and boilerplate.
  - Removed legacy configuration files and unused package-lock.json in favor of pnpm-lock.yaml.

---
