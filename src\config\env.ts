/**
 * Environment variable validation and configuration
 * Ensures all required environment variables are present and valid
 */

interface EnvConfig {
  apiUrl: string;
  nextAuthSecret: string;
  nextAuthUrl: string;
  isDevelopment: boolean;
  isProduction: boolean;
}

/**
 * Validate and parse environment variables
 * Throws error if required variables are missing
 */
function validateEnv(): EnvConfig {
  const apiUrl = process.env.NEXT_PUBLIC_API_URL;
  const nextAuthSecret = process.env.NEXTAUTH_SECRET;
  const nextAuthUrl = process.env.NEXTAUTH_URL;

  if (!apiUrl) {
    throw new Error('NEXT_PUBLIC_API_URL environment variable is required');
  }

  if (!nextAuthSecret) {
    throw new Error('NEXTAUTH_SECRET environment variable is required');
  }

  if (!nextAuthUrl) {
    throw new Error('NEXTAUTH_URL environment variable is required');
  }

  return {
    apiUrl,
    nextAuthSecret,
    nextAuthUrl,
    isDevelopment: process.env.NODE_ENV === 'development',
    isProduction: process.env.NODE_ENV === 'production'
  };
}

export const env = validateEnv();

/**
 * API URL helper function
 * Constructs full API URLs with optional endpoint
 */
export const getApiUrl = (endpoint: string = ''): string => {
  return `${env.apiUrl}${endpoint}`;
};

/**
 * Default admin credentials for development
 * These should match the credentials in your API backend
 */
export const DEV_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'SuperAdmin123!'
};

/**
 * API configuration constants
 */
export const API_CONFIG = {
  TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second
  HEADERS: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
    'X-Afreeserv-Client': 'admin-panel'
  }
} as const;

/**
 * Authentication configuration
 */
export const AUTH_CONFIG = {
  TOKEN_BUFFER_TIME: 5 * 60 * 1000, // 5 minutes buffer before expiry
  SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
  REFRESH_THRESHOLD: 15 * 60 * 1000, // Refresh when 15 minutes left
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION: 15 * 60 * 1000 // 15 minutes lockout
} as const;
